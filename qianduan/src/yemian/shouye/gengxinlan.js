import React, { useState } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';

// 更新栏容器
const Geng<PERSON><PERSON><PERSON><PERSON> = styled.div`
  width: 480px;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `linear-gradient(135deg, ${props.theme.yanse.danjinse}15, ${props.theme.yanse.danjinse_qian}10)`
    : props.theme.yanse.biaomian};
  border: 1px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}40`
    : props.theme.yanse.biankuang};
  border-radius: 15px;
  overflow: hidden;
  margin-top: 16px;
  box-shadow: ${props => props.theme.mingcheng === 'anhei'
    ? `0 4px 12px ${props.theme.yanse.danjinse}20, ${props.theme.yinying.xiao}`
    : props.theme.yinying.xiao};

  @media (max-width: 768px) {
    width: 100%;
    max-width: 480px;
    border-radius: 12px;
  }
`;

// 标签栏容器
const <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> = styled.div`
  display: flex;
  background: ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}08`
    : 'rgba(0, 0, 0, 0.03)'};
  border-bottom: 1px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}30`
    : 'rgba(0, 0, 0, 0.1)'};
`;

// 标签项
const Biaoqianxiang = styled.button`
  flex: 1;
  padding: 12px 16px;
  background: transparent;
  border: none;
  color: ${props => props.huoyue 
    ? props.theme.yanse.wenzi_zhuyao
    : props.theme.yanse.wenzi_ciyao};
  font-size: ${props => props.theme.ziti.daxiao.xiao};
  font-weight: ${props => props.huoyue 
    ? props.theme.ziti.zhongliang.zhongdeng
    : props.theme.ziti.zhongliang.putong};
  cursor: pointer;
  transition: all ${props => props.theme.donghua.sujian.kuai} ${props => props.theme.donghua.huanman.biaozhun};
  position: relative;
  
  &:hover {
    color: ${props => props.theme.mingcheng === 'anhei'
      ? props.theme.yanse.danjinse_hou
      : props.theme.yanse.wenzi_zhuyao};
    background: ${props => props.theme.mingcheng === 'anhei'
      ? `${props.theme.yanse.danjinse}15`
      : 'rgba(0, 0, 0, 0.05)'};
  }

  ${props => props.huoyue && `
    color: ${props.theme.mingcheng === 'anhei'
      ? props.theme.yanse.danjinse
      : props.theme.yanse.wenzi_zhuyao};
    background: ${props.theme.mingcheng === 'anhei'
      ? `${props.theme.yanse.danjinse}20`
      : 'rgba(0, 0, 0, 0.08)'};
  `}

  @media (max-width: 768px) {
    padding: 10px 12px;
    font-size: ${props => props.theme.ziti.daxiao.xiaoxiao};
  }
`;

// 内容区域
const Neirongquyu = styled.div`
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: ${props => props.theme.mingcheng === 'anhei'
      ? `${props.theme.yanse.danjinse}10`
      : 'rgba(0, 0, 0, 0.05)'};
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: ${props => props.theme.mingcheng === 'anhei'
      ? `${props.theme.yanse.danjinse}50`
      : 'rgba(0, 0, 0, 0.2)'};
    border-radius: 2px;
  }

  @media (max-width: 768px) {
    padding: 12px;
    max-height: 300px;
  }
`;

// 更新项
const Gengxinxiang = styled(motion.div)`
  display: flex;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid ${props => props.theme.mingcheng === 'anhei'
    ? `${props.theme.yanse.danjinse}15`
    : 'rgba(0, 0, 0, 0.05)'};

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: ${props => props.theme.mingcheng === 'anhei'
      ? `${props.theme.yanse.danjinse}08`
      : 'rgba(0, 0, 0, 0.02)'};
    border-radius: 8px;
    margin: 0 -8px;
    padding: 12px 8px;
  }
`;

// 更新图标
const Gengxintubiao = styled.div`
  width: 24px;
  height: 24px;
  border-radius: 4px;
  margin-right: 12px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: ${props => props.theme.ziti.zhongliang.cuhei};
  color: white;
  background: ${props => {
    switch (props.leixing) {
      case 'gonggao': return '#10b981'; // 绿色
      case 'huodong': return '#f59e0b'; // 橙色
      case 'xinwen': return '#3b82f6'; // 蓝色
      case 'xitong': return '#8b5cf6'; // 紫色
      case 'jixie': return '#ef4444'; // 红色
      default: return '#6b7280'; // 灰色
    }
  }};

  @media (max-width: 768px) {
    width: 20px;
    height: 20px;
    font-size: 10px;
    margin-right: 8px;
  }
`;

// 更新内容
const Gengxinneirong = styled.div`
  flex: 1;
  min-width: 0;
`;

// 更新标题
const Gengxinbiaoti = styled.div`
  font-size: ${props => props.theme.ziti.daxiao.xiao};
  font-weight: ${props => props.theme.ziti.zhongliang.zhongdeng};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  margin-bottom: 4px;
  line-height: 1.4;
  
  /* 文本溢出处理 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;

  @media (max-width: 768px) {
    font-size: ${props => props.theme.ziti.daxiao.xiaoxiao};
  }
`;

// 更新时间
const Gengxinshijian = styled.div`
  font-size: ${props => props.theme.ziti.daxiao.xiaoxiao};
  color: ${props => props.theme.yanse.wenzi_ciyao};
  
  @media (max-width: 768px) {
    font-size: 10px;
  }
`;

// 空状态
const Kongzhuangtai = styled.div`
  text-align: center;
  padding: 40px 20px;
  color: ${props => props.theme.yanse.wenzi_ciyao};
  font-size: ${props => props.theme.ziti.daxiao.xiao};
`;

// 更新栏组件
function Gengxinlan() {
  const [huoyuebiaoqian, shehuoyuebiaoqian] = useState('suoyou');

  // 模拟数据
  const gengxinshuju = [
    {
      id: 1,
      leixing: 'gonggao',
      biaoti: '查缺更新说明',
      shijian: '2025.04.14 08:31',
      biaoqian: ['suoyou', 'gonggao']
    },
    {
      id: 2,
      leixing: 'jixie',
      biaoti: '【蒸汽朋友圈】经验值、持有率倍率行中！',
      shijian: '2024.05.27 03:45',
      biaoqian: ['suoyou', 'jixie']
    },
    {
      id: 3,
      leixing: 'gonggao',
      biaoti: '2025/08/05(二)维护预告',
      shijian: '2025.08.04 08:30',
      biaoqian: ['suoyou', 'gonggao']
    },
    {
      id: 4,
      leixing: 'huodong',
      biaoti: '暑期打卡生物圈-深海',
      shijian: '2025.08.04 08:30',
      biaoqian: ['suoyou', 'huodong']
    },
    {
      id: 5,
      leixing: 'gonggao',
      biaoti: '2025/07/29(一)维护预告',
      shijian: '2025.07.29 06:30',
      biaoqian: ['suoyou', 'gonggao']
    },
    {
      id: 6,
      leixing: 'gonggao',
      biaoti: '2025/07/29维护预告',
      shijian: '2025.07.28 08:30',
      biaoqian: ['suoyou', 'gonggao']
    },
    {
      id: 7,
      leixing: 'jixie',
      biaoti: '2025/07/23(二)12:15临时维护预告',
      shijian: '2025.07.23 04:15',
      biaoqian: ['suoyou', 'jixie']
    }
  ];

  // 标签配置
  const biaoqianpeizhi = [
    { jian: 'suoyou', mingcheng: '所有' },
    { jian: 'huodong', mingcheng: '活动' },
    { jian: 'xinwen', mingcheng: '新闻' },
    { jian: 'xitong', mingcheng: '系统' },
    { jian: 'jixie', mingcheng: '机械' }
  ];

  // 图标映射
  const tubiaoying = {
    gonggao: '公',
    huodong: '活',
    xinwen: '新',
    xitong: '系',
    jixie: '机'
  };

  // 过滤数据
  const guolvshuju = gengxinshuju.filter(item => 
    huoyuebiaoqian === 'suoyou' || item.biaoqian.includes(huoyuebiaoqian)
  );

  return (
    <Gengxinlanrongqi>
      {/* 标签栏 */}
      <Biaoqianlanrongqi>
        {biaoqianpeizhi.map(biaoqian => (
          <Biaoqianxiang
            key={biaoqian.jian}
            huoyue={huoyuebiaoqian === biaoqian.jian}
            onClick={() => shehuoyuebiaoqian(biaoqian.jian)}
          >
            {biaoqian.mingcheng}
          </Biaoqianxiang>
        ))}
      </Biaoqianlanrongqi>

      {/* 内容区域 */}
      <Neirongquyu>
        <AnimatePresence mode="wait">
          {guolvshuju.length > 0 ? (
            <motion.div
              key={huoyuebiaoqian}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
            >
              {guolvshuju.map(item => (
                <Gengxinxiang
                  key={item.id}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.05 }}
                >
                  <Gengxintubiao leixing={item.leixing}>
                    {tubiaoying[item.leixing]}
                  </Gengxintubiao>
                  <Gengxinneirong>
                    <Gengxinbiaoti>{item.biaoti}</Gengxinbiaoti>
                    <Gengxinshijian>{item.shijian}</Gengxinshijian>
                  </Gengxinneirong>
                </Gengxinxiang>
              ))}
            </motion.div>
          ) : (
            <Kongzhuangtai>
              暂无{biaoqianpeizhi.find(b => b.jian === huoyuebiaoqian)?.mingcheng}更新
            </Kongzhuangtai>
          )}
        </AnimatePresence>
      </Neirongquyu>
    </Gengxinlanrongqi>
  );
}

export default Gengxinlan;
